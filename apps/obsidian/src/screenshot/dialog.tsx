import { ImageEdit, type ImageClipperResult } from "@mx/ui";
import { type App, Modal } from "obsidian";
import ReactDOM from "react-dom/client";
import { ShadowRoot } from "@/components/shadow-dom";

import "./dialog.css";

export class ImageClipDialog extends Modal {
  #disposables: Disposable | null = null;
  #resolvers;
  #input;
  #outputSpec;
  private constructor(
    app: App,
    input: Blob,
    outputSpec?: {
      format?: string;
      quality?: number;
    },
  ) {
    super(app);
    this.#input = input;
    this.#outputSpec = outputSpec;
    this.modalEl.addClass("mx", "image-edit-dialog");
    this.titleEl.setText("Clip Image");
    this.#resolvers = Promise.withResolvers<{
      action: "save" | "copy";
      result: ImageClipperResult;
    } | null>();
    this.#resolvers.promise.finally(() => {
      this.close();
    });
  }
  onOpen(): void {
    super.onOpen();
    using stack = new DisposableStack();
    const root = ReactDOM.createRoot(this.contentEl);
    stack.defer(() => {
      root.unmount();
    });
    stack.defer(() => {
      this.#resolvers.resolve(null);
    });
    root.render(
      <ShadowRoot>
        <ImageEdit
          imageBlob={this.#input}
          outputSpec={this.#outputSpec}
          onSave={(_, result) => {
            this.#resolvers.resolve({ action: "save", result });
          }}
        />
      </ShadowRoot>,
    );
    this.#disposables = stack.move();
  }

  onClose(): void {
    this.#disposables?.[Symbol.dispose]();
    super.onClose();
  }

  static async open(
    app: App,
    image: Blob,
  ): Promise<{ action: "save" | "copy"; result: ImageClipperResult } | null> {
    const modal = new ImageClipDialog(app, image);
    modal.open();
    return await modal.#resolvers.promise;
  }
}
