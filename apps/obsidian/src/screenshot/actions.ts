import { isVideoProvider, MediaPlayerInstance } from "@vidstack/react";
import type { App, Editor, Vault } from "obsidian";
import {
  ButtonComponent,
  normalizePath,
  Notice,
  Platform,
  TFile,
} from "obsidian";

import type { FnScreenshot, PlayerComponent } from "@/def/player-comp";
import { player<PERSON><PERSON> } from "@mx/shared/hooks/use-player";
import { extension } from "@mx/shared/dom/screenshot";
import { toDurationISOString } from "@mx/shared/time/format";
import { getAttachmentFolder } from "@/lib/save-folder";
import {
  insertTimestamp,
  screenshotEmbedFactory,
  timestampLinkFactory,
} from "../link/insert/template";
import type { MediaNoteMeta } from "@/media-lib/parse";
import { ImageClipDialog } from "@/screenshot/dialog";
import { TextExtractionService } from "@/api";
import { UnauthorizedError } from "@/api/base/errors";
import { NoTextFoundError } from "@/api/types/text-extraction";
import * as toast from "@/lib/toast";

export async function extractAndCopyTextFromScreenshot(
  playerComponent: ScreenshotComponent,
) {
  await toast.promise(_extractAndCopyTextFromScreenshot(playerComponent), {
    loading: "Extracting text from screenshot...",
    success: "Text extracted from screenshot and copied to clipboard",
    error: (error) => {
      if (error instanceof UnauthorizedError) {
        return "You need to login to extract text from screenshot";
      }
      if (error instanceof NoTextFoundError) {
        return "No text found in the screenshot";
      }
      if (isCORSErrorMessage(error)) {
        return CORSErrorMessage;
      }
      return `Failed to extract text from screenshot: ${error.message || "Unknown error"}`;
    },
  });
}
async function _extractAndCopyTextFromScreenshot(
  playerComponent: ScreenshotComponent,
) {
  const screenshot = await takeScreenshot(playerComponent, {
    clip: true,
    ...(Platform.isIosApp
      ? { format: "image/jpeg", quality: 0.92 }
      : { format: "image/webp", quality: 0.8 }),
  });
  // Convert Blob to File for text extraction
  const file = new File([screenshot.blob], "screenshot.png", {
    type: screenshot.blob.type,
  });
  const result = await new TextExtractionService().extractText(file);
  console.debug("text extraction result", result);
  // copy plain text to clipboard
  await navigator.clipboard.writeText(result.content);
}

type ScreenshotComponent = Pick<
  PlayerComponent,
  "captureScreenshot" | "plugin" | "getPlayer"
>;

async function takeScreenshot(
  comp: ScreenshotComponent,
  options?: {
    format?: string;
    quality?: number;
    clip?: boolean;
  },
) {
  const settings = await comp.plugin.settings.loaded;
  if (!comp.captureScreenshot) {
    throw new Error("Capture screenshot not implemented");
  }
  const player = comp.getPlayer();
  const spec = {
    format: options?.format ?? settings["playback.screenshot.format"],
    quality: options?.quality ?? settings["playback.screenshot.quality"],
  };
  const screenshot = await comp.captureScreenshot(spec);
  if (options?.clip) {
    const prevPaused = player ? player.paused : null;
    if (player && !prevPaused) {
      player.pause();
    }
    const edited = await ImageClipDialog.open(comp.plugin.app, screenshot.blob);
    if (!edited) {
      throw new Error("Screenshot editing cancelled");
    }
    const blob = await edited.result.blob;
    if (!blob) {
      throw new Error("Failed to clip screenshot");
    }
    if (player && !prevPaused) {
      await player.play();
    }
    return {
      ...screenshot,
      blob,
      size: edited.result.size,
    };
  }
  return screenshot;
}

async function saveScreenshotFile(
  playerComponent: PlayerComponent,
  ctx: { file: TFile; clip?: boolean },
): Promise<{
  file: TFile;
  timestamp: number;
  media: { uid: string; meta: MediaNoteMeta };
}> {
  const { file: newNote } = ctx;
  const player = playerComponent.store.get(playerAtom);
  if (!player) {
    throw new Error("Player not initialized");
  }
  const mediaInfo = playerComponent.getMediaInfo();
  if (!mediaInfo) {
    throw new Error("No media is opened");
  }
  if (!player?.provider || !isVideoProvider(player.provider)) {
    throw new Error("Screenshot is not supported for this media");
  }

  const [{ uid, meta }, settings, { blob, timestamp }] = await Promise.all([
    playerComponent.plugin.mediaItemFor(mediaInfo),
    playerComponent.plugin.settings.loaded,
    takeScreenshot(playerComponent, { clip: ctx.clip }),
  ]);

  const imageDir = await getAttachmentFolder({
    folderPath: settings["playback.screenshot.folder-path"],
    app: playerComponent.plugin.app,
    sourcePath: newNote.path,
  });
  const isoDuration = toDurationISOString(timestamp)
    .toLowerCase()
    .replaceAll(".", "_");
  const imageName = `mx-img-${uid}-${isoDuration}.${extension(blob)}`;

  const file = await saveScreenshotToFile(
    normalizePath(`${imageDir.path}/${imageName}`),
    blob,
    { vault: playerComponent.plugin.app.vault },
  );

  return { file, timestamp, media: { uid, meta } };
}

async function insertScreenshotEmbed<T extends PlayerComponent>(
  playerComponent: T,
  screenshot: {
    file: TFile;
    timestamp: number;
    media: { uid: string; meta: MediaNoteMeta };
  },
  ctx: { file: TFile; editor: Editor },
): Promise<boolean> {
  const { file: newNote, editor } = ctx;
  const { file, timestamp, media } = screenshot;
  const mediaInfo = playerComponent.getMediaInfo();
  if (!mediaInfo) {
    new Notice("No media is opened");
    return false;
  }

  const settings = await playerComponent.plugin.settings.loaded;

  const timestampLink = timestampLinkFactory(
    {
      currentTime: timestamp,
      src: mediaInfo,
    },
    {
      fileManager: playerComponent.plugin.app.fileManager,
      timestampOffset: settings["note.template.timestamp-offset"],
    },
  )(newNote.path);
  const screenshotEmbed = screenshotEmbedFactory(
    {
      file,
      timestamp,
      meta: { uid: media.uid, title: media.meta.title },
    },
    {
      fileManager: playerComponent.plugin.app.fileManager,
      template: settings["note.template.screenshot-embed"],
    },
  )(newNote.path);

  try {
    insertTimestamp(
      {
        timestamp: timestampLink,
        screenshot: screenshotEmbed,
      },
      {
        editor,
        template: settings["note.template.screenshot"],
        insertBefore: settings["note.template.insert-at"] === "before-cursor",
      },
    );
    return true;
  } catch (e) {
    new Notice("Failed to insert screenshot, see console for details");
    console.error("Failed to insert screenshot", e);
    return false;
  }
}

async function _saveScreenshot(
  playerComponent: PlayerComponent,
  ctx: { file: TFile; editor: Editor; clip?: boolean },
): Promise<boolean> {
  const screenshot = await saveScreenshotFile(playerComponent, {
    file: ctx.file,
    clip: ctx.clip,
  });
  if (!screenshot) return false;
  return insertScreenshotEmbed(playerComponent, screenshot, ctx);
}

export async function saveScreenshot(
  playerComponent: PlayerComponent,
  ctx: { file: TFile; editor: Editor; clip?: boolean },
): Promise<boolean> {
  return await toast.promise(_saveScreenshot(playerComponent, ctx), {
    loading: "Saving screenshot...",
    success: `Screenshot saved in "${ctx.file.basename}"`,
    error: (error) => {
      if (isCORSErrorMessage(error)) {
        return CORSErrorMessage;
      }
      return `Failed to save screenshot: ${error.message || "Unknown error"}`;
    },
  });
}

export function isCORSErrorMessage(e: unknown): e is DOMException {
  return (
    e instanceof DOMException &&
    e.name === "SecurityError" &&
    /\btainted\b|\bcanvas/i.test(e.message)
  );
}
export const CORSErrorMessage = createFragment((frag) => {
  const container = frag.createDiv();
  container.appendText(
    "Cannot capture screenshot from this media due to CORS security policy, ",
  );
  container.createEl("br");
  container.appendText("See the troubleshooting guide for possible solutions");

  new ButtonComponent(
    container.createDiv({ attr: { style: "margin-top: 10px" } }),
  )
    .setButtonText("Open guide")
    .onClick(() => {
      window.open("https://mx.pkmer.net/docs/faq/cors", "_blank");
    });
});

// TBD: copy html with timestamp link & screenshot encoded in base64
export async function copyScreenshot(
  player: PlayerComponent,
  options: { clip?: boolean } = {},
): Promise<boolean> {
  try {
    // only png is supported for clipboard
    const screenshot = await takeScreenshot(player, {
      format: "image/png",
      clip: options.clip,
    });
    await copyBlob(screenshot.blob);
    return true;
  } catch (e) {
    new Notice("Failed to copy screenshot, see console for details");
    console.error("Failed to copy screenshot", e);
    return false;
  }
}

export async function copyBlob(blob: Blob) {
  await navigator.clipboard.write([new ClipboardItem({ [blob.type]: blob })]);
}

async function saveScreenshotToFile(
  path: string,
  blob: Blob,
  ctx: { vault: Vault },
): Promise<TFile> {
  const existingFile = ctx.vault.getAbstractFileByPath(path);
  const arrayBuffer = await blob.arrayBuffer();
  if (existingFile instanceof TFile) {
    try {
      await ctx.vault.modifyBinary(existingFile, arrayBuffer);
      new Notice(`Screenshot updated in ${path}`);
      return existingFile;
    } catch (e) {
      new Notice(
        `Failed to save screenshot to ${path}: ${e instanceof Error ? e.message : e}`,
      );
      throw e;
    }
  }
  if (existingFile !== null) {
    new Notice(`Screenshot file occupied by a folder: ${path}`);
    throw new Error(`Screenshot file occupied by a folder: ${path}`);
  }
  try {
    const newFile = await ctx.vault.createBinary(path, arrayBuffer);
    new Notice(`Screenshot created in ${path}`);
    return newFile;
  } catch (e) {
    new Notice(
      `Failed to create screenshot in ${path}: ${e instanceof Error ? e.message : e}`,
    );
    throw e;
  }
}
