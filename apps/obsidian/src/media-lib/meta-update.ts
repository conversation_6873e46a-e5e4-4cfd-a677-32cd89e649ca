import type { TFile } from "obsidian";
import { Notice } from "obsidian";
import type MediaExtended from "@/mx-main";
import type { MediaInfo } from "@/def/media-info";
import { YouTubeService } from "@/api";
import { processMetadataForFrontmatter } from "@/api/utils/metadata-processor";
import { supabase } from "@/auth/supabase";
import { distinct } from "@std/collections";

export interface MetadataUpdateContext {
  plugin: MediaExtended;
}

/**
 * Check if the media is YouTube hosted media
 */
export function isYouTubeMedia(src: MediaInfo): boolean {
  return src.type === "url:hosted" && src.vid.host === "youtube";
}

/**
 * Check if user is logged in to Supabase
 */
export async function isUserLoggedIn(): Promise<boolean> {
  try {
    const { data, error } = await supabase.auth.getSession();
    return !error && !!data.session?.access_token;
  } catch {
    return false;
  }
}

/**
 * Check if metadata update is available for the given media
 */
export function canUpdateMetadata(src: MediaInfo): boolean {
  return isYouTubeMedia(src);
}

/**
 * Check if metadata update is available and user is logged in
 */
export async function isMetadataUpdateAvailable(
  src: MediaInfo,
): Promise<boolean> {
  if (!canUpdateMetadata(src)) {
    return false;
  }
  return await isUserLoggedIn();
}

import * as toast from "@/lib/toast";
import { UnauthorizedError } from "@/api/base/errors";

async function _updateYouTubeMetadata(
  file: TFile,
  videoId: string,
  plugin: MediaExtended,
): Promise<void> {
  const youtubeService = new YouTubeService();
  const metadata = await youtubeService.getVideoMetadata(videoId);

  const { tags, ...meta } = processMetadataForFrontmatter(metadata);

  await plugin.app.fileManager.processFrontMatter(file, (frontmatter) => {
    Object.assign(frontmatter, meta);
    const newTags = distinct([...(frontmatter.tags ?? []), ...tags]);
    frontmatter.tags = newTags.slice(0, 15);
  });
}

/**
 * Update YouTube metadata for a media note
 */
export async function updateYouTubeMetadata(
  file: TFile,
  videoId: string,
  plugin: MediaExtended,
): Promise<void> {
  return await toast.promise(_updateYouTubeMetadata(file, videoId, plugin), {
    loading: "Fetching YouTube metadata...",
    success: `YouTube metadata updated successfully: ${file.path}`,
    error: (error) => {
      if (error instanceof UnauthorizedError) {
        return "You need to login to get latest YouTube metadata";
      }
      if (error instanceof Error) {
        return `Failed to fetch YouTube metadata: ${error.message}`;
      }
      return "Failed to fetch YouTube metadata";
    },
  });
}

/**
 * Update metadata for any supported media type
 */
export async function updateMediaMetadata(
  file: TFile,
  mediaInfo: MediaInfo,
  ctx: MetadataUpdateContext,
): Promise<void> {
  if (!canUpdateMetadata(mediaInfo)) {
    new Notice("Metadata update is only supported for YouTube videos.");
    return;
  }

  const loggedIn = await isUserLoggedIn();
  if (!loggedIn) {
    new Notice("Please log in to update YouTube metadata.");
    return;
  }

  if (mediaInfo.type === "url:hosted" && mediaInfo.vid.host === "youtube") {
    await updateYouTubeMetadata(file, mediaInfo.vid.vid, ctx.plugin);
  }
}
