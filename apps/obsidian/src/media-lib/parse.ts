import {
  parseFrontMatterEntry,
  type CachedMetadata,
  type FrontMatterCache,
} from "obsidian";
import { parseTextTrackFields } from "@/transcript/resolve/parse-fm";
import type { MetaTextTrackInfo } from "@/def/track-info";
import * as v from "valibot";
import type { PlayerFlipOption } from "@mx/ui";

export interface MediaNoteMeta {
  title: string;
  textTracks: MetaTextTrackInfo[];
  aspectRatio: string | undefined;
  crossOrigin: CrossOriginValue;
  flip: PlayerFlipOption;
}

export const defaultMediaMeta: Readonly<MediaNoteMeta> = {
  title: "",
  textTracks: [],
  crossOrigin: false,
  aspectRatio: undefined,
  flip: "none",
};

const flipSchema = v.fallback(
  v.picklist(["horizontal", "vertical", "both", "none"]),
  "none",
);
const aspectRatioSchema = v.fallback(v.optional(v.string()), undefined);

export function parseMediaNoteMeta(meta: CachedMetadata): MediaNoteMeta {
  return {
    title: title(meta),
    textTracks: parseTextTrackFields(meta),
    aspectRatio: v.parse(
      aspectRatioSchema,
      parseFrontMatterEntry(meta.frontmatter, "aspect_ratio"),
    ),
    crossOrigin: v.parse(
      crossOriginSchema,
      parseFrontMatterEntry(meta.frontmatter, "cross_origin"),
    ),
    flip: v.parse(flipSchema, parseFrontMatterEntry(meta.frontmatter, "flip")),
  };
}

function title(meta: CachedMetadata) {
  const fromH1 = meta.headings?.find((h) => h.level === 1)?.heading;
  const fromFm = meta.frontmatter?.title;
  if (typeof fromFm === "string" && fromFm) return fromFm.trim();
  if (fromH1) return fromH1.trim();
  return "";
}

/**
 * non-empty string or true are accepted as cross origin enabled,
 * those are not "use-credentials" will be treated as anonymous
 */
const crossOriginTruthy = v.pipe(
  v.union([v.pipe(v.string(), v.nonEmpty()), v.literal(true)]),
  v.transform((v) =>
    v === "use-credentials" ? "use-credentials" : "anonymous",
  ),
);
const crossOriginSchema = v.fallback(
  v.union([crossOriginTruthy, v.literal(false)]),
  false,
);
export type CrossOriginValue = "use-credentials" | "anonymous" | false;
