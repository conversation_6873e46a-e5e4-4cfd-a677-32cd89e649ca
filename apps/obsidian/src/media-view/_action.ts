import type { PlayerComponent } from "@/def/player-comp";
import {
  saveScreenshot,
  extractAndCopyTextFromScreenshot,
} from "@/screenshot/actions";
import { insertTimestampLink } from "@/link/insert/timestamp";
import { getMostRecentEditorLeaf } from "@/link/leaf/finder";
import { player<PERSON><PERSON> } from "@mx/shared/hooks/use-player";
import { isVideoProvider, type AnyMediaProvider } from "@vidstack/react";
import { observe } from "jotai-effect";
import {
  type App,
  type ItemView,
  Menu,
  requestUrl,
  type TFile,
  type Vault,
  type View,
} from "obsidian";
import { Notice } from "obsidian";
import MediaFileView from "./file-view";
import MediaUrlView from "./url-view";
import { mediaFlipAtom, mediaMetaAtom } from "@/def/atom/media-meta";
import { mediaIconAtom } from "@/def/atom/media-icon";
import { loadedMediaTracksAtom } from "@/def/atom/track";
import { showAtButton } from "@/lib/menu";
import { importTextTrackFile, importTextTrackUrl } from "@/transcript/import";
import { requireFs } from "@/lib/node";
import { fileToTrack, urlToTrackLink, type TrackMeta } from "@/def/track-info";
import { inferTrackInfoFromPath } from "@/transcript/resolve/infer-info";
import { nanoid } from "nanoid";
import { addTracksToFrontmatter } from "@/transcript/resolve/parse-fm";
import type { MediaInfo } from "@/def/media-info";
import type MxPlugin from "@/mx-main";
import type { CaptionsFileFormat } from "media-captions";
import { getAttachmentFolder } from "@/lib/save-folder";

declare module "obsidian" {
  interface WorkspaceLeaf {
    updateHeader(): void;
  }
  interface View {
    titleEl: HTMLElement;
  }
  interface Workspace {
    requestActiveLeafEvents(): boolean;
  }
}

async function saveSubtitleContent(
  {
    media,
    content,
    meta,
  }: { media: MediaInfo; content: string; meta: Omit<TrackMeta, "wid" | "id"> },
  ctx: {
    plugin: MxPlugin;
    app: App;
  },
): Promise<void> {
  const mediaProps = await ctx.plugin.mediaItemFor(media);
  const filename = [mediaProps.uid, trackId(), meta.language]
    .filter((v) => !!v)
    .join(".");

  const settings = await ctx.plugin.settings.loaded;

  const trackFolder = await getAttachmentFolder({
    app: ctx.app,
    sourcePath: mediaProps.note.path,
    folderPath: settings["playback.track.folder-path"],
  });

  const file = await saveTrackToFile(
    `${trackFolder.path}/${filename}.${meta.format}`,
    content,
    { vault: ctx.app.vault },
  );

  const trackinfo = fileToTrack(file, meta);

  if (!trackinfo) {
    throw new Error("Failed to create track info");
  }

  await addTracksToFrontmatter([trackinfo], {
    fileManager: ctx.app.fileManager,
    note: mediaProps.note,
    metadataCache: ctx.app.metadataCache,
  });

  new Notice(`Subtitle saved to ${file.path}`);
}

export function onPaneMenu(
  view: PlayerComponent & View,
  menu: Menu,
  source: "more-options" | "tab-header" | string,
) {
  const info = view.getMediaInfo();
  if (!info) return;

  view.app.workspace.trigger(
    "mx:media-menu",
    menu,
    {
      plugin: view.plugin,
      player: view.getPlayer(),
      src: info,
      tracks: view.store.get(loadedMediaTracksAtom)?.trackInfo ?? [],
      captureScreenshot: view.captureScreenshot?.bind(view),
      takeTimestamp: view.takeTimestamp?.bind(view),
      setFlip: (flip) => {
        view.store.set(mediaFlipAtom, flip);
      },
      flip: view.store.get(mediaMetaAtom).flip,
    },
    source,
  );
}

async function saveTrackToFile(
  path: string,
  content: string,
  ctx: { vault: Vault },
): Promise<TFile> {
  const existingFile = ctx.vault.getAbstractFileByPath(path);
  if (existingFile) {
    new Notice(`Failed to save text track to ${path}: file already exists`);
  }
  try {
    const newFile = await ctx.vault.create(path, content);
    new Notice(`Text track saved in ${path}`);
    return newFile;
  } catch (e) {
    new Notice(
      `Failed to save text track in ${path}: ${e instanceof Error ? e.message : e}`,
    );
    throw e;
  }
}

export function updateHeader(
  view: PlayerComponent & View,
  { disableTitleUpdate }: { disableTitleUpdate?: boolean } = {},
) {
  if (disableTitleUpdate) {
    return observe((get) => {
      get(mediaIconAtom);
      view.leaf.updateHeader();
    }, view.store);
  }
  return observe((get) => {
    get(mediaIconAtom);
    get(mediaMetaAtom).title;
    // if (
    //   app.workspace.activeLeaf === view.leaf &&
    //   app.workspace.requestActiveLeafEvents()
    // ) {
    //   view.leaf.updateHeader();
    // }
    view.leaf.updateHeader();
    view.titleEl.setText(view.store.get(mediaMetaAtom).title || "Media");
  }, view.store);
}

export function addAction(comp: PlayerComponent & ItemView) {
  using stack = new DisposableStack();
  if (comp.takeTimestamp) {
    comp.addAction("star", "Take timestamp in last active note", async () => {
      const editorLeaf = getMostRecentEditorLeaf(comp.app.workspace);
      if (!editorLeaf) {
        new Notice("Please open a note before taking timestamp");
        return;
      }
      await insertTimestampLink(comp, editorLeaf.view);
      // success &&
      //   new Notice(`Timestamp taken in "${editorLeaf.view.file.basename}"`);
    });
  }
  if (comp.captureScreenshot) {
    const register = (action: HTMLElement) => {
      if (!(comp instanceof MediaFileView || comp instanceof MediaUrlView))
        return;
      action.style.display = "none";
      stack.defer(
        observe((get) => {
          const player = get(playerAtom);
          if (!player) return;
          const onProviderChange = (provider: AnyMediaProvider | null) => {
            if (isVideoProvider(provider)) {
              action.style.display = "";
            } else {
              action.style.display = "none";
            }
          };
          onProviderChange(player.provider);
          return player.listen("provider-change", (evt) =>
            onProviderChange(evt.target.provider),
          );
        }, comp.store),
      );
    };
    for (const { clip, name, icon } of [
      { clip: true, name: "Clip and Save Screenshot", icon: "frame" },
      { clip: false, name: "Save Screenshot", icon: "camera" },
    ]) {
      register(
        comp.addAction(icon, name, () => {
          const editorLeaf = getMostRecentEditorLeaf(comp.app.workspace);
          if (!editorLeaf) {
            new Notice("Please open a note before capturing screenshot");
            return;
          }
          saveScreenshot(comp, {
            editor: editorLeaf.view.editor,
            file: editorLeaf.view.file,
            clip,
          });
        }),
      );
    }
    register(
      comp.addAction("scan-text", "Extract text from screenshot", () =>
        extractAndCopyTextFromScreenshot(comp),
      ),
    );
  }
  comp.addAction("plus", "Add resources", (evt) => {
    const menu = new Menu();
    menu.addSections(["action", "view"]);
    const media = comp.getMediaInfo();
    if (!media) return;
    const mediaPropsNote = comp.plugin.mediaLib.findNoteByMedia(media);
    menu
      .addItem((item) =>
        item
          .setIcon("captions")
          .setSection("action")
          .setTitle("Add text track")
          .setIsLabel(true),
      )
      .addItem((item) =>
        item
          .setIcon("file-plus")
          .setSection("action")
          .setTitle("from local file")
          .onClick(async () => {
            const form = await importTextTrackFile(comp.app);
            if (!form) return;
            const meta = inferTrackInfoFromPath(form.file);
            if (!meta) return;

            await saveSubtitleContent(
              {
                media,
                content:
                  form.file instanceof File
                    ? await form.file.text()
                    : await requireFs().readFile(form.file.path, "utf-8"),
                meta: {
                  kind: form["track-kind"],
                  format: meta.format,
                  language: form.language || meta.language,
                  label: form.label || "",
                  isDefault: form["default-enable"],
                },
              },
              comp,
            );
          }),
      )
      .addItem((item) =>
        item
          .setIcon("link")
          .setSection("action")
          .setTitle("from remote URL")
          .onClick(async () => {
            const form = await importTextTrackUrl(comp.app);
            if (!form) return;
            if (form["should-download"]) {
              const response = await requestUrl(form.url);
              const meta = inferTrackInfoFromPath({ path: form.url });
              // we need exact format so we can save file with correct extension
              const format =
                form.format ||
                toCaptionsFileFormat(response.headers["content-type"]) ||
                meta?.format;
              if (!format) {
                new Notice("Could not determine subtitle format from URL");
                return;
              }
              await saveSubtitleContent(
                {
                  media,
                  content: response.text,
                  meta: {
                    kind: form["track-kind"],
                    format,
                    language: form.language || meta?.language || null,
                    label: form.label || "",
                    isDefault: form["default-enable"],
                  },
                },
                comp,
              );
            } else {
              const mediaProps = await comp.plugin.mediaItemFor(media);
              // we don't save file here, let media-captions do auto-detect
              const trackinfo = urlToTrackLink(new URL(form.url), {
                kind: form["track-kind"],
                language: form.language,
                label: form.label,
                isDefault: form["default-enable"],
                id: trackId(),
              });
              if (!trackinfo) return;
              await addTracksToFrontmatter([trackinfo], {
                fileManager: comp.app.fileManager,
                note: mediaProps.note,
                metadataCache: comp.app.metadataCache,
              });
            }
          }),
      );

    if (mediaPropsNote) {
      menu.addItem((item) => {
        item
          .setTitle("Open media properties")
          .setIcon("table-properties")
          .setSection("view")
          .onClick(() => {
            comp.app.workspace.openLinkText(mediaPropsNote.path, "", "tab");
          });
      });
    } else {
      menu.addItem((item) => {
        item
          .setTitle("Add to media library")
          .setIcon("badge-plus")
          .setSection("action")
          .onClick(async () => {
            const mediaProps = await comp.plugin.mediaItemFor(media);
            new Notice(`Added to media library: ${mediaProps.note.path}`);
          });
      });
    }
    showAtButton(evt, menu);
  });
  return stack.move();
}

function toCaptionsFileFormat(
  mime: string | undefined,
): CaptionsFileFormat | undefined {
  switch (mime) {
    case "text/vtt":
      return "vtt";
    case "application/x-subrip":
      return "srt";
    case "application/x-ssa":
      return "ssa";
    case "application/x-ass":
      return "ass";
    default:
      return undefined;
  }
}

function trackId() {
  return nanoid(4);
}
