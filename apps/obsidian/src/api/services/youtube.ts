import * as v from "valibot";
import { BaseApiService } from "../base/base-service";
import { YouTubeMetadataSchema } from "../types/youtube";
import type { YouTubeMetadata } from "../types/youtube";
import { BaseApiError, NotFoundError, UnauthorizedError } from "../base/errors";

/**
 * Service for fetching YouTube video metadata
 */
export class YouTubeService extends BaseApiService {
  /**
   * Fetches YouTube video metadata with progress notification
   * @param videoId - YouTube video ID
   * @returns Promise resolving to YouTube metadata or null if user is not logged in
   */
  async getVideoMetadata(videoId: string): Promise<YouTubeMetadata> {
    if (!videoId?.trim()) {
      throw new BaseApiError("Video ID is required");
    }

    // Validate video ID format
    this.#validateVideoId(videoId);

    // Check if user is logged in
    if (!(await this.isAuthenticated())) {
      throw new UnauthorizedError(
        "You need to login to fetch YouTube metadata",
      );
    }
    try {
      const metadataPromise = await this.makeAuthenticatedRequest(
        `/youtube/video/${videoId}/metadata`,
        {
          method: "GET",
        },
        (data) => v.parse(YouTubeMetadataSchema, data),
      );
      return metadataPromise;
    } catch (error) {
      // Re-throw with more specific context if needed
      if (error instanceof NotFoundError) {
        throw new NotFoundError(`YouTube video not found: ${videoId}`);
      }
      throw error;
    }
  }

  /**
   * Validates YouTube video ID format
   */
  #validateVideoId(videoId: string): void {
    const trimmedId = videoId.trim();

    // Basic validation for YouTube video ID format
    // YouTube video IDs are typically 11 characters long and contain alphanumeric characters, hyphens, and underscores
    const videoIdPattern = /^[a-zA-Z0-9_-]{11}$/;

    if (!videoIdPattern.test(trimmedId)) {
      throw new BaseApiError(
        "Invalid YouTube video ID format. Video ID should be 11 characters long and contain only alphanumeric characters, hyphens, and underscores.",
      );
    }
  }

  /**
   * Extracts video ID from various YouTube URL formats
   * @param url - YouTube URL
   * @returns Video ID or null if not found
   */
  static extractVideoId(url: string): string | null {
    try {
      const urlObj = new URL(url);

      // Handle different YouTube URL formats
      if (urlObj.hostname.includes("youtube.com")) {
        // Standard youtube.com URLs
        if (urlObj.pathname === "/watch") {
          return urlObj.searchParams.get("v");
        }
        // youtube.com/embed/ URLs
        if (urlObj.pathname.startsWith("/embed/")) {
          return urlObj.pathname.split("/embed/")[1]?.split("?")[0] || null;
        }
        // youtube.com/v/ URLs
        if (urlObj.pathname.startsWith("/v/")) {
          return urlObj.pathname.split("/v/")[1]?.split("?")[0] || null;
        }
      }

      // Handle youtu.be short URLs
      if (urlObj.hostname === "youtu.be") {
        return urlObj.pathname.slice(1).split("?")[0] || null;
      }

      return null;
    } catch {
      // If URL parsing fails, check if the input is already a video ID
      if (/^[a-zA-Z0-9_-]{11}$/.test(url.trim())) {
        return url.trim();
      }
      return null;
    }
  }

  /**
   * Validates if a string is a valid YouTube URL or video ID
   * @param input - URL or video ID to validate
   * @returns boolean indicating if input is valid
   */
  static isValidYouTubeInput(input: string): boolean {
    if (!input?.trim()) return false;

    // Check if it's a valid video ID
    if (/^[a-zA-Z0-9_-]{11}$/.test(input.trim())) {
      return true;
    }

    // Check if it's a valid YouTube URL that contains a video ID
    const videoId = YouTubeService.extractVideoId(input);
    return videoId !== null;
  }
}
