import * as v from "valibot";
import { BaseApiService } from "../base/base-service";
import {
  TextExtractionResponseSchema,
  MAX_IMAGE_SIZE,
  TextExtractionError,
  FileSizeExceededError,
  NoTextFoundError,
  TextExtractionApiError,
  SUPPORTED_IMAGE_TYPES,
  UnsupportedImageTypeError,
} from "../types/text-extraction";
import { UnauthorizedError } from "../base/errors";
import { assertNever } from "@std/assert/unstable-never";

/**
 * Result interface for text extraction with metadata
 */
export interface TextExtractionResult {
  /** Extracted text content in markdown format */
  content: string;
  /** Metadata about the extracted content */
  metadata: {
    /** Whether the content contains tables */
    has_tables: boolean;
    /** Whether the content contains lists */
    has_lists: boolean;
    /** Number of text regions detected */
    text_regions_count: number;
  };
}

/**
 * Service for extracting text from images using OCR
 */
export class TextExtractionService extends BaseApiService {
  /**
   * Extracts text from an image file and returns it with metadata
   * @param imageFile - Image file to extract text from
   * @returns Promise resolving to extracted text result with metadata
   * @throws {NoTextFoundError} When no text is found in the image
   * @throws {TextExtractionApiError} When the API returns an error status
   * @throws {UnauthorizedError} When user is not authenticated
   * @throws {TextExtractionError} For other extraction errors
   */
  async extractText(imageFile: Blob): Promise<TextExtractionResult> {
    if (!imageFile) {
      throw new TextExtractionError("Image file is required");
    }

    // Check if user is logged in
    if (!(await this.isAuthenticated())) {
      throw new UnauthorizedError(
        "You need to login to extract text from screenshot",
      );
    }

    // Validate the image file
    this.#validateImageFile(imageFile);

    // Create FormData with the image file
    const formData = this.#createFormData(imageFile);

    const extractionPromise = this.makeAuthenticatedRequest(
      "/text-extract",
      {
        method: "POST",
        body: formData,
      },
      (data) => v.parse(TextExtractionResponseSchema, data),
    );

    try {
      const response = await extractionPromise;

      // Handle different response statuses
      switch (response.status) {
        case "success":
          return {
            content: response.content,
            metadata: response.metadata,
          };

        case "no_text_found":
          throw new NoTextFoundError("No text found in the provided image");

        case "error":
          throw new TextExtractionApiError(
            response.content || "Text extraction service returned an error",
          );

        default:
          assertNever(response.status);
      }
    } catch (error) {
      // Re-throw known errors as-is
      if (
        error instanceof NoTextFoundError ||
        error instanceof TextExtractionApiError ||
        error instanceof TextExtractionError
      ) {
        throw error;
      }

      // Wrap unknown errors
      throw new TextExtractionError(
        `Failed to extract text from image: ${error instanceof Error ? error.message : "Unknown error"}`,
        undefined,
        error instanceof Error ? error : undefined,
      );
    }
  }

  /**
   * Extracts text from an image file and returns only the content string
   * This method provides backward compatibility for existing code
   * @param imageFile - Image file to extract text from
   * @returns Promise resolving to extracted markdown text
   * @deprecated Use extractText() instead for full metadata
   */
  async extractTextContent(imageFile: Blob): Promise<string> {
    const result = await this.extractText(imageFile);
    return result.content;
  }

  /**
   * Validates the image file before upload
   */
  #validateImageFile(file: Blob): void {
    if (!v.is(SUPPORTED_IMAGE_TYPES, file.type)) {
      throw new UnsupportedImageTypeError(file.type);
    }

    // Check file size
    if (file.size > MAX_IMAGE_SIZE) {
      throw new FileSizeExceededError(file.size);
    }

    // Check if file is empty
    if (file.size === 0) {
      throw new TextExtractionError("Image file is empty");
    }
  }

  /**
   * Creates FormData with the image file
   */
  #createFormData(imageFile: Blob): FormData {
    const formData = new FormData();
    formData.append("image", imageFile);
    return formData;
  }

  /**
   * Gets the maximum allowed file size in bytes
   */
  static getMaxImageSize(): number {
    return MAX_IMAGE_SIZE;
  }

  /**
   * Formats file size in human readable format
   */
  static formatFileSize(bytes: number): string {
    if (bytes === 0) return "0 Bytes";

    const k = 1024;
    const sizes = ["Bytes", "KB", "MB", "GB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));

    return `${Number.parseFloat((bytes / k ** i).toFixed(2))} ${sizes[i]}`;
  }
}
