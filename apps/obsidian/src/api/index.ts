/**
 * API services and utilities for the Media Extended plugin
 */

// Base classes and common utilities
export { BaseApiService } from "./base/base-service";
export type {
  RequestOptions,
  RetryConfig,
  ApiEndpointConfig,
} from "./base/types";

// Common error types
export {
  BaseApiError,
  NetworkError,
  AuthError,
  NotFoundError,
  ProcessingError,
  ValidationError,
  RateLimitError,
  ServerError,
} from "./base/errors";

// Services
export { YouTubeService } from "./services/youtube";
export { TextExtractionService } from "./services/text-extraction";

// YouTube types
export type { YouTubeMetadata, YouTubeThumbnail } from "./types/youtube";
export { YouTubeMetadataSchema, YouTubeThumbnailSchema } from "./types/youtube";

// Text extraction types
export type { TextExtractionResult } from "./services/text-extraction";
export type {
  TextExtractionResponse,
  TextExtractionStatus,
} from "./types/text-extraction";
export {
  TextExtractionResponseSchema,
  MAX_IMAGE_SIZE,
  TextExtractionError,
  TextExtractionAuthError,
  TextExtractionNetworkError,
  TextExtractionProcessingError,
  TextExtractionValidationError,
  FileSizeExceededError,
  NoTextFoundError,
  TextExtractionApiError,
} from "./types/text-extraction";

// Utilities
export {
  normalizeTags,
  cleanDescription,
  selectBestThumbnail,
  processMetadataForFrontmatter,
} from "./utils/metadata-processor";
