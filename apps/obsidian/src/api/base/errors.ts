/**
 * Base error classes for API services
 */
export class BaseApiError extends Error {
  #statusCode?: number;
  #originalError?: Error;

  constructor(message: string, statusCode?: number, originalError?: Error) {
    super(message);
    this.name = "BaseApiError";
    this.#statusCode = statusCode;
    this.#originalError = originalError;
  }

  get statusCode(): number | undefined {
    return this.#statusCode;
  }

  get originalError(): Error | undefined {
    return this.#originalError;
  }
}

export class NetworkError extends BaseApiError {
  constructor(message: string, originalError?: Error) {
    super(message, undefined, originalError);
    this.name = "NetworkError";
  }
}

export class AuthError extends BaseApiError {
  constructor(message: string, statusCode?: number) {
    super(message, statusCode);
    this.name = "AuthError";
  }
}

export class UnauthorizedError extends AuthError {
  constructor(message: string, statusCode?: number) {
    super(message, statusCode);
    this.name = "UnauthorizedError";
  }
}

export class NotFoundError extends BaseApiError {
  constructor(message: string, statusCode = 404) {
    super(message, statusCode);
    this.name = "NotFoundError";
  }
}

export class ProcessingError extends BaseApiError {
  constructor(message: string, statusCode = 202) {
    super(message, statusCode);
    this.name = "ProcessingError";
  }
}

export class ValidationError extends BaseApiError {
  constructor(message: string, originalError?: Error) {
    super(message, 400, originalError);
    this.name = "ValidationError";
  }
}

export class RateLimitError extends BaseApiError {
  constructor(message = "Rate limit exceeded. Please try again later.") {
    super(message, 429);
    this.name = "RateLimitError";
  }
}

export class ServerError extends BaseApiError {
  constructor(message: string, statusCode: number) {
    super(message, statusCode);
    this.name = "ServerError";
  }
}
