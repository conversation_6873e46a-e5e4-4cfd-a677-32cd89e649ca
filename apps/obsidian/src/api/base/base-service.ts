import { supabase } from "@/auth/supabase";
import { retry } from "@std/async";
import {
  BaseApiError,
  NetworkError,
  AuthError,
  NotFoundError,
  ProcessingError,
  ValidationError,
  RateLimitError,
  ServerError,
} from "./errors";
import type { RequestOptions, RetryConfig } from "./types";

/**
 * Base class for API services with common functionality
 */
export abstract class BaseApiService {
  #retryConfig: RetryConfig = {
    maxAttempts: 3,
    minTimeout: 2000,
    maxTimeout: 16000,
    multiplier: 2,
    jitter: 0.1,
  };

  #apiHost: string;

  constructor(apiHost?: string) {
    const host = apiHost || process.env.API_HOST;
    if (!host) {
      throw new Error("API_HOST is not set");
    }
    this.#apiHost = host;
  }

  /**
   * Gets access token from Supabase auth
   */
  async #getAccessToken(): Promise<string> {
    try {
      const { data, error } = await supabase.auth.getSession();

      if (error) {
        throw new AuthError(`Authentication error: ${error.message}`);
      }

      const accessToken = data.session?.access_token;
      if (!accessToken) {
        throw new AuthError("No access token available. Please log in.");
      }

      return accessToken;
    } catch (error) {
      if (error instanceof AuthError) {
        throw error;
      }
      throw new AuthError("Failed to get authentication token");
    }
  }

  /**
   * Makes a request to the API with proper error handling
   */
  async #makeRequest(
    endpoint: string,
    options: RequestOptions = {},
  ): Promise<Response> {
    const accessToken = await this.#getAccessToken();
    const timeout = options.timeout ?? 30000;

    const defaultHeaders: Record<string, string> = {
      Authorization: `Bearer ${accessToken}`,
    };

    // Don't set Content-Type for FormData - let browser set it with boundary
    if (!(options.body instanceof FormData)) {
      defaultHeaders["Content-Type"] = "application/json";
    }

    const requestOptions: RequestInit = {
      method: options.method ?? "GET",
      headers: { ...defaultHeaders, ...options.headers },
      body: options.body,
      signal: AbortSignal.timeout(timeout),
    };

    try {
      const response = await fetch(
        `${this.#apiHost}${endpoint}`,
        requestOptions,
      );
      return response;
    } catch (error) {
      if (error instanceof Error) {
        if (error.name === "AbortError") {
          throw new NetworkError("Request timeout", error);
        }
        if (error.name === "TypeError" && error.message.includes("fetch")) {
          throw new NetworkError("Network connection failed", error);
        }
      }
      throw new NetworkError("Network request failed", error as Error);
    }
  }

  /**
   * Executes a request with retry logic for processing responses
   */
  async #fetchWithRetry<T>(
    requestFn: () => Promise<Response>,
    validator: (data: any) => T,
  ): Promise<T> {
    // Retry only the request until the backend finishes processing
    const response = await retry(async () => {
      const response = await requestFn();
      // 202 → still processing → retry
      if (response.status === 202) {
        throw new ProcessingError("Request is still being processed");
      }
      return response;
    }, this.#retryConfig);

    // Convert non-OK responses into typed errors (no retry)
    if (!response.ok) {
      await this.#handleErrorResponse(response);
    }

    // Validate the response data
    try {
      const data = await response.json();
      return validator(data);
    } catch (error) {
      throw new ValidationError("Invalid response format", error as Error);
    }
  }

  /**
   * Handles error responses from the API
   */
  async #handleErrorResponse(response: Response): Promise<never> {
    const status = response.status;

    try {
      const errorData = await response.json();
      const message =
        errorData.error || errorData.message || response.statusText;

      switch (status) {
        case 401:
        case 403:
          throw new AuthError(`Authentication failed: ${message}`, status);
        case 404:
          throw new NotFoundError(`Resource not found: ${message}`, status);
        case 429:
          throw new RateLimitError();
        case 500:
        case 502:
        case 503:
        case 504:
          throw new ServerError(`Server error: ${message}`, status);
        default:
          throw new BaseApiError(`API error: ${message}`, status);
      }
    } catch (parseError) {
      // If we can't parse the error response, use the status text
      switch (status) {
        case 401:
        case 403:
          throw new AuthError("Authentication failed", status);
        case 404:
          throw new NotFoundError("Resource not found", status);
        case 429:
          throw new RateLimitError();
        default:
          throw new BaseApiError(
            `HTTP ${status}: ${response.statusText}`,
            status,
          );
      }
    }
  }

  /**
   * Checks if user is authenticated
   */
  protected async isAuthenticated(): Promise<boolean> {
    try {
      const { data, error } = await supabase.auth.getSession();
      return !error && !!data.session?.access_token;
    } catch {
      return false;
    }
  }

  /**
   * Protected method for subclasses to make authenticated requests
   */
  protected async makeAuthenticatedRequest<T>(
    endpoint: string,
    options: RequestOptions,
    validator: (data: any) => T,
  ): Promise<T> {
    const result = await this.#fetchWithRetry(
      () => this.#makeRequest(endpoint, options),
      validator,
    );

    return result;
  }

  /**
   * Protected method for simple authenticated requests without retry logic
   */
  protected async makeSimpleRequest<T>(
    endpoint: string,
    options: RequestOptions,
    validator: (data: any) => T,
  ): Promise<T> {
    const response = await this.#makeRequest(endpoint, options);

    if (!response.ok) {
      await this.#handleErrorResponse(response);
    }

    try {
      const data = await response.json();
      return validator(data);
    } catch (error) {
      throw new ValidationError("Invalid response format", error as Error);
    }
  }
}
