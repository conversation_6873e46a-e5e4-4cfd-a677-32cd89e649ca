/**
 * Common types for API services
 */
export interface RequestOptions {
  method?: string;
  headers?: Record<string, string>;
  body?: FormData | string;
  timeout?: number;
}

export interface RetryConfig {
  maxAttempts: number;
  minTimeout: number;
  maxTimeout: number;
  multiplier: number;
  jitter?: number;
}

export interface ApiEndpointConfig {
  path: string;
  method?: string;
  timeout?: number;
  requiresAuth?: boolean;
}
