import { distinctBy } from "@std/collections";
import type { YouTubeMetadata } from "../types/youtube";

/**
 * Normalizes tag to comply with Obsidian tag format requirements
 * @param tag - Raw tag string
 * @returns Normalized tag or null if invalid
 * @see https://help.obsidian.md/tags#Tag+format
 */
function normalizeTag(tag: string): string | null {
  // Remove leading/trailing whitespace
  // and convert to lowercase for consistency (obsidian tags are case-insensitive)
  let normalized = tag.trim().toLowerCase();

  // Replace spaces and invalid characters with hyphens
  normalized = normalized
    .replace(/\s+/g, "-") // Replace spaces with hyphens
    .replace(/[^a-z0-9_-]/g, "") // Remove invalid characters (keep only letters, numbers, underscore, hyphen)
    .replace(/-+/g, "-") // Replace multiple consecutive hyphens with single hyphen
    .replace(/^-+|-+$/g, ""); // Remove leading/trailing hyphens

  // Ensure tag starts with a letter or underscore (Obsidian requirement)
  if (!/^[a-z_]/.test(normalized)) {
    // If starts with number or invalid char, prefix with underscore
    normalized = `_${normalized}`;
  }

  // Check length limits (reasonable for Obsidian)
  if (normalized.length === 0 || normalized.length > 50) return null;

  return normalized;
}

/**
 * Cleans and standardizes YouTube tags for Obsidian
 * @param tags - Raw tags array
 * @returns Cleaned, normalized, and deduplicated tags compliant with Obsidian format
 */
export function normalizeTags(tags: string[]): string[] {
  return distinctBy(
    tags
      .map(normalizeTag) // Normalize each tag to Obsidian format
      .filter((tag): tag is string => tag !== null), // Remove invalid tags
    (v) => v.toLocaleLowerCase(),
  ).slice(0, 15); // Limit to 15 tags to avoid frontmatter bloat
}

/**
 * Truncates and cleans description text
 * @param description - Raw description text
 * @param maxLength - Maximum length (default: 500)
 * @returns Cleaned and truncated description
 */
export function cleanDescription(description: string, maxLength = 500): string {
  // Remove excessive whitespace and normalize line breaks
  const cleaned = description
    .replace(/\r\n/g, "\n")
    .replace(/\r/g, "\n")
    .replace(/\n{3,}/g, "\n\n")
    .trim();

  if (cleaned.length <= maxLength) return cleaned;

  // Truncate at word boundary
  const truncated = cleaned.substring(0, maxLength);
  const lastSpace = truncated.lastIndexOf(" ");

  if (lastSpace > maxLength * 0.8) {
    return `${truncated.substring(0, lastSpace)}...`;
  }

  return `${truncated}...`;
}

/**
 * Selects the best thumbnail URL based on available options
 * @param thumbnails - Available thumbnails
 * @returns Best thumbnail URL or null if none available
 */
export function selectBestThumbnail(
  thumbnails: YouTubeMetadata["thumbnails"],
): string | null {
  if (!thumbnails || typeof thumbnails !== "object") return null;

  // Priority order: maxres > high > standard > medium > default
  const priorities: (keyof typeof thumbnails)[] = [
    "maxres",
    "high",
    "standard",
    "medium",
    "default",
  ];

  for (const quality of priorities) {
    const thumbnail = thumbnails[quality];
    if (thumbnail?.url) {
      return thumbnail.url;
    }
  }

  return null;
}

/**
 * Processes raw YouTube metadata for Obsidian frontmatter
 * @param metadata - Raw YouTube metadata
 * @returns Processed metadata ready for frontmatter
 */
export function processMetadataForFrontmatter(metadata: YouTubeMetadata) {
  return {
    title: metadata.title || metadata.id,
    description: cleanDescription(metadata.description),
    duration: metadata.duration,
    published_at: metadata.published_at,
    uploader: metadata.uploader_name,
    view_count: metadata.view_count,
    like_count: metadata.like_count,
    aspect_ratio: metadata.aspect_ratio,
    cover: selectBestThumbnail(metadata.thumbnails),
    language: metadata.language,
    tags: normalizeTags(metadata.tags),
  };
}
