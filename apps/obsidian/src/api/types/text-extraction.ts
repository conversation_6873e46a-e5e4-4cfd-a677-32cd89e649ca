import * as v from "valibot";
import {
  BaseApiError,
  AuthError,
  NetworkError,
  ProcessingError,
  ValidationError,
} from "../base/errors";

const TextExtractionStatusSchema = v.picklist([
  "success",
  "no_text_found",
  "error",
]);

/**
 * Schema definitions for text extraction API
 */
export const TextExtractionResponseSchema = v.object({
  status: TextExtractionStatusSchema,
  content: v.string(),
  metadata: v.object({
    has_tables: v.boolean(),
    has_lists: v.boolean(),
    text_regions_count: v.pipe(v.number(), v.integer(), v.minValue(0)),
  }),
});

// Inferred types
export type TextExtractionResponse = v.InferOutput<
  typeof TextExtractionResponseSchema
>;

export type TextExtractionStatus = v.InferOutput<
  typeof TextExtractionStatusSchema
>;

/**
 * Maximum file size for image uploads (4MB)
 */
export const MAX_IMAGE_SIZE = 4 * 1024 * 1024;

export const SUPPORTED_IMAGE_TYPES = v.picklist([
  "image/png",
  "image/jpeg",
  "image/webp",
  "image/heic",
  "image/heif",
]);

/**
 * Text extraction specific error types
 */
export class TextExtractionError extends BaseApiError {
  constructor(message: string, statusCode?: number, originalError?: Error) {
    super(message, statusCode, originalError);
    this.name = "TextExtractionError";
  }
}

export class TextExtractionAuthError extends AuthError {
  constructor(message: string, statusCode?: number) {
    super(message, statusCode);
    this.name = "TextExtractionAuthError";
  }
}

export class TextExtractionNetworkError extends NetworkError {
  constructor(message: string, originalError?: Error) {
    super(message, originalError);
    this.name = "TextExtractionNetworkError";
  }
}

export class TextExtractionProcessingError extends ProcessingError {
  constructor(message: string) {
    super(message);
    this.name = "TextExtractionProcessingError";
  }
}

export class TextExtractionValidationError extends ValidationError {
  constructor(message: string, originalError?: Error) {
    super(message, originalError);
    this.name = "TextExtractionValidationError";
  }
}

export class FileSizeExceededError extends TextExtractionError {
  constructor(size: number) {
    const sizeMB = (size / (1024 * 1024)).toFixed(2);
    const maxSizeMB = (MAX_IMAGE_SIZE / (1024 * 1024)).toFixed(0);
    super(
      `File size ${sizeMB}MB exceeds maximum allowed size of ${maxSizeMB}MB`,
      413,
    );
    this.name = "FileSizeExceededError";
  }
}

export class UnsupportedImageTypeError extends TextExtractionError {
  constructor(type: string) {
    super(`Unsupported image type: ${type}`);
    this.name = "UnsupportedImageTypeError";
  }
}

/**
 * Error thrown when no text is found in the image
 */
export class NoTextFoundError extends TextExtractionError {
  constructor(message = "No text found in the provided image") {
    super(message, 200);
    this.name = "NoTextFoundError";
  }
}

/**
 * Error thrown when the API returns an error status
 */
export class TextExtractionApiError extends TextExtractionError {
  constructor(message = "Text extraction service returned an error") {
    super(message, 500);
    this.name = "TextExtractionApiError";
  }
}
