/**
 * YouTube metadata schemas and types for Obsidian plugin UI
 * Based on the API response from /apps/api/src/routes/youtube.ts
 */

import * as v from "valibot";

// Schema definitions
export const YouTubeThumbnailSchema = v.object({
  url: v.pipe(v.string(), v.url()),
  size: v.optional(
    v.object({
      width: v.pipe(v.number(), v.integer(), v.minValue(1)),
      height: v.pipe(v.number(), v.integer(), v.minValue(1)),
    }),
  ),
});

export const YouTubeMetadataSchema = v.object({
  /** Video ID */
  id: v.pipe(v.string(), v.minLength(1)),
  /** Video title */
  title: v.pipe(v.string(), v.minLength(1)),
  /** Video description */
  description: v.string(),
  /** Duration in seconds */
  duration: v.pipe(v.number(), v.minValue(0)),
  /** Publication date in ISO 8601 format */
  published_at: v.pipe(v.string(), v.isoTimestamp()),
  /** Channel/uploader name */
  uploader_name: v.pipe(v.string(), v.minLength(1)),
  /** View count, null if unavailable */
  view_count: v.nullable(v.pipe(v.number(), v.integer(), v.minValue(0))),
  /** Like count, null if unavailable */
  like_count: v.nullable(v.pipe(v.number(), v.integer(), v.minValue(0))),
  /** Aspect ratio as string (e.g., "16 / 9"), null if unavailable */
  aspect_ratio: v.nullable(v.pipe(v.string(), v.minLength(1))),
  /** Available thumbnails by quality */
  thumbnails: v.partial(
    v.object({
      default: YouTubeThumbnailSchema,
      high: YouTubeThumbnailSchema,
      maxres: YouTubeThumbnailSchema,
      medium: YouTubeThumbnailSchema,
      standard: YouTubeThumbnailSchema,
    }),
  ),
  /** Video language code */
  language: v.string(),
  /** Video tags */
  tags: v.array(v.string()),
});

// Inferred types from schemas
export type YouTubeThumbnail = v.InferOutput<typeof YouTubeThumbnailSchema>;
export type YouTubeMetadata = v.InferOutput<typeof YouTubeMetadataSchema>;
