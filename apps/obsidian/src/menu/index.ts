import type MediaExtended from "@/mx-main";
import { isModEvent } from "@/patch/mod-evt";
import { parseFile } from "@/def/file-parse";
import { parseUrl } from "@/def/url-parse";
import { trackMenu, transcriptMenu } from "./part/track";
import { muteMenu } from "./part/mute";
import { embedMenu } from "./part/embed";
import { mediaLibMenu } from "./part/media-lib";
import { speedMenu } from "./part/speed";
import { TFile, Notice } from "obsidian";
import { urlMenu } from "./part/url";
import { screenshotMenu } from "./part/screenshot";
import { flipMenu } from "./part/flip";
import {
  canUpdateMetadata,
  isUserLoggedIn,
  updateMediaMetadata,
} from "@/media-lib/meta-update";

export function registerMenuHandler(plugin: MediaExtended) {
  plugin.registerEvent(
    plugin.app.workspace.on("mx:media-menu", (menu, ctx, source) => {
      if (source === "player-menu-embed") {
        embedMenu(menu, { ...ctx, plugin });
      }
      if (source === "tab-header") {
        muteMenu(menu, ctx);
      }
      if (source === "player-menu-view" || source === "player-menu-embed") {
        trackMenu(menu, { ...ctx, plugin });
        speedMenu(menu, { ...ctx, plugin });
        flipMenu(menu, ctx);
      }
      if (
        source !== "player-menu-view" &&
        // let file-menu handle it
        ctx.src.type !== "file"
      ) {
        mediaLibMenu(menu, { ...ctx, plugin });
        urlMenu(menu, ctx, { source: "player" });
      }
      if (source !== "player-menu-view") {
        transcriptMenu(menu, { ...ctx, plugin });
      }
      screenshotMenu(menu, ctx);
    }),
  );
  plugin.registerEvent(
    plugin.app.workspace.on("file-menu", (menu, file) => {
      if (!(file instanceof TFile)) return;
      // if being media note
      const mediaFromNote = plugin.mediaLib.findMediaByNote(file);
      if (mediaFromNote) {
        menu.addItem((item) => {
          item
            .setTitle("Open related media")
            .setIcon("square-play")
            .setSection("open")
            .onClick((evt) => {
              plugin.link.openMedia(
                { info: mediaFromNote.src },
                { fromUser: true, newLeaf: isModEvent(evt) },
              );
            });
        });

        // Add Update Media Metadata menu item
        menu.addItem((item) => {
          const canUpdate = canUpdateMetadata(mediaFromNote.src);

          item
            .setTitle("Update media metadata")
            .setIcon("refresh-cw")
            .setSection("action");

          // Check conditions and set disabled state if needed
          if (!canUpdate) {
            item.setDisabled(true);
          }

          item.onClick(async () => {
            await updateMediaMetadata(file, mediaFromNote.src, { plugin });
          });
        });
      }
      const media = parseFile(file);
      if (media) {
        mediaLibMenu(menu, { src: media, plugin });
      }
    }),
  );
  plugin.registerEvent(
    plugin.app.workspace.on("url-menu", (menu, link) => {
      const media = parseUrl(link);
      if (media) {
        menu.addItem((item) => {
          item
            .setTitle("Open in Obsidian player")
            .setIcon("square-play")
            .setSection("open")
            .onClick((evt) => {
              plugin.link.openMedia(media, {
                fromUser: true,
                newLeaf: isModEvent(evt),
              });
            });
        });
        mediaLibMenu(menu, { src: media.info, plugin });
        urlMenu(menu, { src: media.info }, { source: "url-menu" });
      }
    }),
  );
}
