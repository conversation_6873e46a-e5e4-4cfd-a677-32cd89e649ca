import type MxPlugin from "@/mx-main";
import { addMediaViewCommand } from "./_base";
import { insertTimestampLink } from "@/link/insert/timestamp";
import { Notice } from "obsidian";
import { copyScreenshot, saveScreenshot } from "@/screenshot/actions";

export function registerNoteCommands(plugin: MxPlugin) {
  addMediaViewCommand(
    {
      id: "take-timestamp",
      name: "Take timestamp",
      icon: "star",
      checkCallback(checking, view, note) {
        if (!view.takeTimestamp || !note?.editor || !note.ctx.file)
          return false;
        if (checking) return true;
        const file = note.ctx.file;
        const editor = note.editor;
        insertTimestampLink(view, { file, editor }).then((success) => {
          success && new Notice(`Timestamp taken in "${file.basename}"`);
        });
      },
    },
    plugin,
  );
  for (const { clip, name, id } of [
    {
      clip: true,
      name: {
        save: "Clip and Save Screenshot",
        copy: "Clip and Copy Screenshot",
      },
      id: "-clip",
    },
    {
      clip: false,
      name: { save: "Save Screenshot", copy: "Copy Screenshot" },
      id: "",
    },
  ]) {
    addMediaViewCommand(
      {
        id: `save-screenshot${id}`,
        name: name.save,
        icon: "camera",
        checkCallback(checking, view, note) {
          if (!view.captureScreenshot || !note?.editor || !note.ctx.file)
            return false;
          if (checking) return true;
          const file = note.ctx.file;
          const editor = note.editor;
          saveScreenshot(view, { file, editor, clip });
        },
      },
      plugin,
    );
    addMediaViewCommand(
      {
        id: `copy-screenshot${id}`,
        name: name.copy,
        icon: "focus",
        checkCallback(checking, view, note) {
          if (!view.captureScreenshot || !note) return false;
          if (checking) return true;
          copyScreenshot(view, { clip }).then((success) => {
            success && new Notice("Screenshot copied to clipboard");
          });
        },
      },
      plugin,
    );
  }
}
