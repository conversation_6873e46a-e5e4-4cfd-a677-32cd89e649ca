import { Notice } from "obsidian";

/**
 * Toast utility with sonner-like API for handling loading states and notifications
 */

interface ToastPromiseOptions<T> {
  loading: string;
  success: ((data: T) => string | DocumentFragment) | string | DocumentFragment;
  error:
    | ((error: any) => string | DocumentFragment)
    | string
    | DocumentFragment;
  /** Don't immediately show the loading toast, wait for the specified delay */
  loadingDelay?: number;
  successDuration?: number;
  errorDuration?: number;
}

const LOADING_SYMBOL = Symbol("loading");

class LazyNotice implements Disposable {
  #notice: Notice | null = null;

  setMessage(message: string | DocumentFragment) {
    if (!this.#notice) {
      this.#notice = new Notice(message, 0);
    } else {
      this.#notice.setMessage(message);
    }
  }

  [Symbol.dispose]() {
    this.#notice?.hide();
  }
}

async function promiseToast<T>(
  promise: Promise<T>,
  options: ToastPromiseOptions<T>,
): Promise<void> {
  const {
    loading,
    loadingDelay = 200,
    successDuration = 2000,
    errorDuration = 4000,
  } = options;

  using notice = new LazyNotice();

  // Race between the promise and the loading delay
  const loadingTimeout =
    loadingDelay > 0 ? sleep(loadingDelay) : Promise.resolve();

  // Wait for either the promise to complete or the loading delay
  const raceResult = await Promise.race([
    promise,
    loadingTimeout.then(() => LOADING_SYMBOL),
  ]);

  if (raceResult === LOADING_SYMBOL) {
    // Promise is still pending, show loading toast
    notice.setMessage(loading);
  }

  try {
    const result = await promise;
    const successMessage =
      typeof options.success === "function"
        ? options.success(result)
        : options.success;
    notice.setMessage(successMessage);
    await sleep(successDuration);
  } catch (error) {
    const errorMessage =
      typeof options.error === "function"
        ? options.error(error)
        : options.error;
    console.error(
      errorMessage instanceof DocumentFragment
        ? errorMessage.textContent
        : errorMessage,
      error,
    );
    notice.setMessage(errorMessage);
    await sleep(errorDuration);
  }
}

export const promise = async <T>(
  promise: Promise<T>,
  options: ToastPromiseOptions<T>,
): Promise<T> => {
  promiseToast(promise, options);
  return await promise;
};
