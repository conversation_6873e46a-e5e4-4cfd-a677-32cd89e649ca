import { Platform, requireApiVersion } from "obsidian";

export function openUrlExternally(url: string) {
  // this seems not working, use electron.shell.openExternal even for 1.9.0+ for now
  // https://obsidian.md/changelog/#:~:text=You%20can%20now%20bypass%20the%20Web%20viewer%20and%20specify%20that%20a%20URL%20is%20opened%20in%20the%20user%27s%20default%20browser%20using%20window.open(url%2C%20%27external%27).
  // if (Platform.isDesktopApp && !requireApiVersion("1.9.0")) {
  if (Platform.isDesktopApp) {
    require("electron").shell.openExternal(url);
    return;
  }
  window.open(url, "external");
}
