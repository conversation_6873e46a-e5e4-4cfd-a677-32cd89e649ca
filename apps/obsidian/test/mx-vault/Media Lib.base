formulas:
  titles: if(!title.isEmpty(), title, if(!video.isEmpty(), video, if(!audio.isEmpty(), audio, if(!media.isEmpty(), media, ""))))
  media: ' if(!video.isEmpty(), video, if(!audio.isEmpty(), audio, if(!media.isEmpty(), media, "")))'
views:
  - type: cards
    name: View
    filters:
      and:
        - '!note["mx-uid"].isEmpty()'
    order:
      - formula.titles
      - formula.media
    sort:
      - property: file.ctime
        direction: DESC
    image: cover
    imageAspectRatio: 0.55
