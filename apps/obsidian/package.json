{"name": "@mx/obsidian", "version": "4.0.0-beta.3", "description": "", "private": true, "type": "module", "scripts": {"dev": "NODE_ENV=development node --env-file=.env esbuild.config.js", "typecheck": "tsc", "build": "tsc && NODE_ENV=production node --env-file=.env esbuild.config.js"}, "keywords": [], "author": "AidenLx", "devDependencies": {"@codemirror/language": "workspace:*", "@codemirror/state": "^6.0.0", "@codemirror/view": "^6.23.0", "@electron/remote": "^2.1.2", "@types/core-js": "^2.5.8", "@types/cors": "^2.8.17", "@types/mdast": "^4.0.4", "@types/node": "22.14.1", "@types/react": "19.1.7", "@types/react-dom": "19.1.6", "@types/ws": "^8.18.0", "builtin-modules": "3.3.0", "electron": "^32.1.2", "esbuild": "^0.25.0", "globby": "^14.1.0", "jszip": "^3.10.1", "obsidian": "1.8.7", "semver": "^7.5.4", "typescript": "~5.8.2"}, "dependencies": {"@mx/ext-fn": "workspace:*", "@mx/shared": "workspace:*", "@mx/ui": "workspace:*", "@paralleldrive/cuid2": "^2.2.2", "@std/assert": "npm:@jsr/std__assert@^1.0.11", "@std/async": "npm:@jsr/std__async@^1.0.13", "@std/collections": "npm:@jsr/std__collections@^1.0.10", "@std/encoding": "npm:@jsr/std__encoding@^1.0.9", "@supabase/supabase-js": "^2.47.12", "@vidstack/react": "npm:@aidenlx/vidstack-react@1.12.13-mod.3", "core-js-pure": "^3.41.0", "cors": "^2.8.5", "filenamify": "^6.0.0", "jotai": "^2.12.2", "jotai-effect": "^1.1.6", "lucide-react": "^0.475.0", "maverick.js": "^0.43.2", "media-captions": "^0.0.18", "monkey-around": "^3.0.0", "nanoid": "^5.0.9", "prosekit": "^0.11.5", "react": "19.1.0", "react-dom": "19.1.0", "remark-gfm": "^4.0.1", "remark-parse": "^11.0.0", "superjson": "^2.2.2", "unified": "^11.0.5", "unist-util-visit": "^5.0.0", "valibot": "^1.1.0", "ws": "^8.18.1"}}