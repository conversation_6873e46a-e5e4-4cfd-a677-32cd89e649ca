import { retry, type RetryOptions } from "@std/async";

/**
 * Determines if an HTTP status code should be retried
 */
function isRetryableStatus(status: number): boolean {
  // 5xx server errors - server might recover
  if (status >= 500 && status < 600) {
    return true;
  }

  // 429 Too Many Requests - rate limiting
  if (status === 429) {
    return true;
  }

  // 408 Request Timeout
  if (status === 408) {
    return true;
  }

  // 502 Bad Gateway, 503 Service Unavailable, 504 Gateway Timeout
  if ([502, 503, 504].includes(status)) {
    return true;
  }

  return false;
}

export async function fetchWithRetry(
  url: string,
  {
    signal,
    retry: {
      maxAttempts = 3,
      minTimeout = 1000,
      maxTimeout = 10000,
      ...retryOptions
    } = {},
    ...options
  }: RequestInit & { retry?: RetryOptions } = {},
) {
  return await retry(
    async () => {
      const resp = await fetch(url, {
        signal: signal ?? AbortSignal.timeout(5000),
        ...options,
      });
      if (isRetryableStatus(resp.status)) {
        throw new Error(`Retryable status: ${resp.status}`);
      }
      return resp;
    },
    {
      maxAttempts,
      minTimeout,
      maxTimeout,
      ...retryOptions,
    },
  );
}
