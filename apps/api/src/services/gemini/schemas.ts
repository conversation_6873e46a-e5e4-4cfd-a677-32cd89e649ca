import * as v from "valibot";
import { toJsonSchema } from "@valibot/to-json-schema";

/**
 * Schema for cleaned description output from Gemini API
 */
const ParsedCleanedDescriptionSchema = v.object({
  cleanedText: v.pipe(
    v.string(),
    v.minLength(1, "Cleaned text cannot be empty"),
    v.maxLength(1500, "Cleaned text cannot be longer than 1500 characters"),
  ),
});

export const CleanedDescriptionSchema = v.pipe(
  v.string(),
  v.parseJson(),
  ParsedCleanedDescriptionSchema,
);

/**
 * TypeScript type for cleaned description output
 */
export type CleanedDescriptionOutput = v.InferOutput<
  typeof ParsedCleanedDescriptionSchema
>;

/**
 * JSON Schema for Gemini API structured output
 */
export const CleanedDescriptionJSONSchema = toJsonSchema(
  ParsedCleanedDescriptionSchema,
);

const ParsedTextExtractSchema = v.object({
  status: v.picklist(["success", "no_text_found", "error"]),
  content: v.string(),
  metadata: v.object({
    has_tables: v.boolean(),
    has_lists: v.boolean(),
    text_regions_count: v.pipe(v.number(), v.minValue(0), v.integer()),
  }),
});

export const TextExtractSchema = v.pipe(
  v.string(),
  v.parseJson(),
  ParsedTextExtractSchema,
);

export const TextExtractJSONSchema = {
  $schema: "http://json-schema.org/draft-07/schema#",
  type: "object",
  properties: {
    status: {
      type: "string", // to make gemini happy, valibot outputs "enum" type without this
      enum: ["success", "no_text_found", "error"],
    },
    content: {
      type: "string",
    },
    metadata: {
      type: "object",
      properties: {
        has_tables: {
          type: "boolean",
        },
        has_lists: {
          type: "boolean",
        },
        text_regions_count: {
          type: "integer",
          minimum: 0,
        },
      },
      required: ["has_tables", "has_lists", "text_regions_count"],
    },
  },
  required: ["status", "content", "metadata"],
};

export type TextExtractOutput = v.InferOutput<typeof ParsedTextExtractSchema>;
