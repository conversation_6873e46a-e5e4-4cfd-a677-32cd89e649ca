import * as v from "valibot";

/**
 * Maximum file size for image uploads (4MB)
 */
export const MAX_IMAGE_SIZE = 4 * 1024 * 1024;

/**
 * Supported image MIME types for text extraction
 */
export const SUPPORTED_IMAGE_TYPES = v.picklist([
  "image/png",
  "image/jpeg",
  "image/webp",
  "image/heic",
  "image/heif",
]);

/**
 * Schema for validating image files from FormData
 */
export const GeminiImageBlobSchema = v.pipe(
  v.blob("Image is required"),
  v.check((blob) => blob.size > 0, "Image cannot be empty"),
  v.check(
    (blob) => blob.size <= MAX_IMAGE_SIZE,
    `Image size cannot exceed ${MAX_IMAGE_SIZE} bytes (4MB)`,
  ),
  v.check(
    (blob) => v.is(SUPPORTED_IMAGE_TYPES, blob.type),
    "Unsupported image type.",
  ),
  v.brand("GeminiImageBlob"),
);

export type GeminiImageBlob = v.InferOutput<typeof GeminiImageBlobSchema>;

/**
 * Schema for validating FormData containing an image file
 */
export const TextExtractFormDataSchema = v.object({
  image: GeminiImageBlobSchema,
});

/**
 * Type for validated form data
 */
export type TextExtractFormData = v.InferOutput<
  typeof TextExtractFormDataSchema
>;

/**
 * Validation error messages
 */
export const ValidationErrors = {
  NO_IMAGE_FILE: "Image file is required",
  EMPTY_IMAGE_FILE: "Image file cannot be empty",
  FILE_SIZE_EXCEEDED: `Image file size cannot exceed ${MAX_IMAGE_SIZE} bytes (4MB)`,
  UNSUPPORTED_IMAGE_TYPE:
    "Unsupported image type. Supported types: PNG, JPEG, WebP, HEIC, HEIF",
} as const;
