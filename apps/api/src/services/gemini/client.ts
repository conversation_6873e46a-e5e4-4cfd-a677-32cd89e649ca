import {
  GoogleGenAI,
  type ContentListUnion,
  type GenerateContentConfig,
} from "@google/genai";
import { getLogger } from "@logtape/logtape";
import { errorToJSON } from "../../lib/logging";

const logger = getLogger(["mx-api", "gemini", "client"]);

export interface ImageInput {
  mimeType: string;
  sizeBytes: number;
  base64Data: string;
}

/**
 * Low-level client for Google Gemini API interactions
 */
export class GeminiClient {
  private ai: GoogleGenAI;

  constructor(apiKey: string) {
    this.ai = new GoogleGenAI({ apiKey });
  }

  /**
   * Generate content with text input and structured output
   */
  async generate(
    contents: ContentListUnion,
    {
      model = "gemini-2.5-flash",
      ...config
    }: GenerateContentConfig & { model?: string } = {},
  ): Promise<string> {
    const startTime = Date.now();

    try {
      const response = await this.ai.models.generateContent({
        model,
        contents,
        config,
      });

      const duration = Date.now() - startTime;
      const responseText = response.text ?? "";

      logger.info("Text content generation completed", {
        duration,
        outputLength: responseText.length,
        model,
        usage: response.usageMetadata,
      });

      return responseText;
    } catch (error) {
      const duration = Date.now() - startTime;
      logger.error("Text content generation failed", {
        duration,
        error: errorToJSON(error),
      });
      throw error;
    }
  }
}
