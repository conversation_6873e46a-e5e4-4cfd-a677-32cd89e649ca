import { nanoid } from "nanoid";
import type { YtDlpYouTubeMetadata, YtDlpYoutubeSubtitle } from "./apify";
import type { YouTubeApiMetadata, YouTubeApiSubtitle } from "./youtube-api";
import type { Pipel<PERSON>, Redis } from "@upstash/redis/cloudflare";
import { pick } from "@std/collections";
import { getLogger } from "@logtape/logtape";
import { errorToJSON, redactSensitiveData } from "../lib/logging";

const logger = getLogger(["mx-api", "cache"]);

// Simplified cache types without expiration tracking - Redis handles expiration

// Subtitle list cache without expiration tracking
export type YtdlpVideoSubtitleListCache = ({
  id: string;
} & YtdlpVideoSubtitleListCacheItem)[];

interface YtdlpVideoSubtitleListCacheItem {
  ast: boolean;
  name: string | undefined;
}

export const HASH_CACHEAT_KEY = "__CACHED_AT__" as const;

// Cache result types for stale-while-revalidate logic
export type CacheResult<T> = {
  data: T;
  isStale: boolean;
} | null;

// Lock status for distributed locking - Redis handles expiration
export type CacheLockInfo = { lock_id: string };

export type CacheLock = AsyncDisposable & {
  acquired: { key: string; id: string } | null;
};

// Cache key patterns
export const CACHE_KEYS = {
  metadata: (videoId: string) => `yt-dlp:metadata:${videoId}`,
  subtitleUrlHash: (videoId: string) => `yt-dlp:subtitles-urls:${videoId}`,
  subtitleListHash: (videoId: string) => `yt-dlp:subtitles-list:${videoId}`,
  // YouTube API cache keys
  youtubeApiMetadata: (videoId: string) => `yt-api:metadata:${videoId}`,
  youtubeApiSubtitles: (videoId: string) => `yt-api:subtitles:${videoId}`,
  // 404 tombstone cache keys
  notFound: (videoId: string) => `yt:404:video:${videoId}`,
  // Gemini AI cleaned description cache keys
  cleanedDescription: (hash: string) => `gemini:cleaned-desc:${hash}`,
} as const;
export const LOCK_KEYS = {
  ytdlp: (videoId: string) => `lock:yt-dlp:${videoId}`,
  ytapi_metadata: (videoId: string) => `lock:yt-api:metadata:${videoId}`,
  ytapi_subtitles: (videoId: string) => `lock:yt-api:subtitles:${videoId}`,
  gemini_clean_description: (hash: string) => `lock:gemini:clean-desc:${hash}`,
};

// Default expiration times (in seconds)
export const CACHE_EXPIRATION = {
  metadata: 7 * 24 * 60 * 60, // 7 days
  subtitleList: 24 * 60 * 60, // 1 day
  subtitle: 4 * 60 * 60, // 4 hours (fallback, prefer URL expiration, 7 hours in general)
  lock: 3 * 60, // 3 minutes (max processing time)
  // YouTube API cache expiration
  youtubeApi: 6 * 60 * 60, // 6 hours
  youtubeApiSubtitles: 7 * 24 * 60 * 60, // 7 days, it take 50 quota per call...
  // 404 tombstone cache expiration (shorter TTL to allow for recovery)
  notFound: 4 * 60 * 60, // 4 hours
  // Gemini AI cleaned description cache expiration
  cleanedDescription: 7 * 24 * 60 * 60, // 7 days
} as const;

/**
 * Comprehensive cache service with distributed locking for Apify action management
 * Uses Redis TTL for automatic cache invalidation
 */
export class VideoCacheService {
  #redis: Redis;
  pipeline() {
    return this.#redis.pipeline();
  }

  constructor(redis: Redis) {
    this.#redis = redis;
  }

  /**
   * Check if cache is stale based on TTL (< 25% of original expiration time)
   * @param ttl - TTL in seconds
   * @param originalExpiration - Original expiration time in seconds
   * @returns true if cache is stale, false otherwise
   */
  #isStale(
    ttl: number,
    expiry: { exat: number; cached_at: number } | { ex: number },
  ): boolean {
    if (ttl <= 0) return true; // Expired or doesn't exist
    const expiryDuration =
      "exat" in expiry ? expiry.exat - expiry.cached_at : expiry.ex;
    return ttl < expiryDuration * 0.25;
  }

  /**
   * Acquire distributed lock for video processing
   * Returns lock info if successful, null if already locked
   */
  async acquireLock(lockKey: string): Promise<CacheLock> {
    logger.info("Attempting to acquire cache lock", { lockKey });

    const lockId = nanoid();
    const lock = { key: lockKey, id: lockId };

    logger.debug("Attempting Redis lock acquisition", {
      ...lock,
      expiration: CACHE_EXPIRATION.lock,
    });

    // Use Redis SET with NX (only if not exists) and EX (expiration)
    const result = await this.#redis.set<CacheLockInfo>(
      lockKey,
      { lock_id: lockId },
      {
        nx: true, // Only set if key doesn't exist
        ex: CACHE_EXPIRATION.lock, // Expire after lock timeout
      },
    );

    if (result === "OK") {
      logger.info("Cache lock acquired successfully", lock);
      return {
        acquired: lock,
        [Symbol.asyncDispose]: async () => {
          await this.releaseLock(lock);
        },
      };
    }

    logger.warn("Failed to acquire cache lock - already exists", lock);
    return {
      acquired: null,
      [Symbol.asyncDispose]: async () => {},
    };
  }

  /**
   * Release lock atomically using Lua script (called by webhooks on success/failure)
   * Only releases the lock if the lock ID matches, preventing race conditions
   */
  async releaseLock(lock: { key: string; id: string }): Promise<boolean> {
    logger.info("Attempting to release lock", { lock });

    const result = await this.#redis.eval<[string], 0 | 1 | -1>(
      `
local current = redis.call('GET', KEYS[1])
if current == false then
  return 0  -- Key doesn't exist
end

local currentData = cjson.decode(current)
if currentData.lock_id == ARGV[1] then
  redis.call('DEL', KEYS[1])
  return 1  -- Successfully deleted
else
  return -1  -- Lock ID mismatch
end
    `.trim(),
      [lock.key],
      [lock.id],
    );

    if (result === 1) {
      logger.info("Cache lock released successfully", { lock });
      return true;
    }

    if (result === 0) {
      logger.warn("Lock release failed - lock not found", { lock });
      return false;
    }

    logger.warn("Lock release failed - lock ID mismatch", { lock });
    return false;
  }

  /**
   * Get cached metadata with stale check using Redis pipeline
   */
  async getMetadata(
    videoId: string,
  ): Promise<CacheResult<YtDlpYouTubeMetadata>> {
    logger.debug("Fetching cached yt-dlp metadata", { videoId });

    const cacheKey = CACHE_KEYS.metadata(videoId);

    // Use Redis pipeline to get both data and TTL in single call
    const [data, ttl] = await this.#redis
      .pipeline()
      .get<YtDlpYouTubeMetadata>(cacheKey)
      .ttl(cacheKey)
      .exec();

    if (!data) {
      logger.debug("yt-dlp metadata cache miss", {
        videoId,
      });
      return null;
    }

    const isStale = this.#isStale(ttl, { ex: CACHE_EXPIRATION.metadata });

    logger.info("yt-dlp metadata cache hit", {
      videoId,
      title: data.title,
      isStale,
      ttl,
    });

    return {
      data,
      isStale,
    };
  }

  /**
   * Cache metadata with Redis TTL
   */
  async setMetadata(
    videoId: string,
    metadata: YtDlpYouTubeMetadata,
    { redis = this.#redis }: { redis?: Redis | Pipeline } = {},
  ): Promise<void> {
    logger.info("Caching yt-dlp metadata", {
      videoId,
      title: metadata.title,
      duration: metadata.duration,
      expiration: CACHE_EXPIRATION.metadata,
    });

    const cacheKey = CACHE_KEYS.metadata(videoId);
    await redis.set<YtDlpYouTubeMetadata>(cacheKey, metadata, {
      ex: CACHE_EXPIRATION.metadata,
    });

    logger.debug("yt-dlp metadata cached successfully", {
      videoId,
      title: metadata.title,
    });
  }

  /**
   * Get cached subtitle list with stale check using Redis pipeline
   */
  async getSubtitleList(
    videoId: string,
  ): Promise<CacheResult<YtdlpVideoSubtitleListCache>> {
    if (videoId === HASH_CACHEAT_KEY) return null;

    logger.debug("Fetching cached subtitle list", { videoId });

    const hashKey = CACHE_KEYS.subtitleListHash(videoId);

    // Get all hash fields and TTL in a single pipeline
    const [hashData, hashTtl] = await this.#redis
      .pipeline()
      .hgetall(hashKey)
      .ttl(hashKey)
      .exec();

    if (!hashData || Object.keys(hashData).length === 1) {
      logger.debug("Subtitle list cache miss", {
        videoId,
        hasData: !!hashData,
        keyCount: hashData ? Object.keys(hashData).length : 0,
      });
      return null;
    }

    // Filter out the cache metadata key and convert to subtitle list format
    const subtitleEntries = Object.entries(hashData).filter(
      (entries): entries is [string, YtdlpVideoSubtitleListCacheItem] =>
        entries[0] !== HASH_CACHEAT_KEY,
    );

    if (subtitleEntries.length === 0) {
      logger.debug("Subtitle list cache empty after filtering", {
        videoId,
        totalKeys: Object.keys(hashData).length,
      });
      return null;
    }

    const data: YtdlpVideoSubtitleListCache = subtitleEntries.map(
      ([id, value]) => ({ ...value, id }),
    );

    const isStale = this.#isStale(hashTtl, {
      ex: CACHE_EXPIRATION.subtitleList,
    });

    logger.info("Subtitle list cache hit", {
      videoId,
      subtitleCount: data.length,
      astCount: data.filter((s) => s.ast).length,
      isStale,
      ttl: hashTtl,
    });

    return {
      data,
      isStale,
    };
  }

  /**
   * Cache subtitle list with Redis TTL using hash storage
   */
  async setSubtitleList(
    videoId: string,
    subtitles: YtDlpYoutubeSubtitle[],
    { redis = this.#redis }: { redis?: Redis | Pipeline } = {},
  ): Promise<void> {
    if (subtitles.length === 0) return;

    const hashKey = CACHE_KEYS.subtitleListHash(videoId);
    const records = {
      ...Object.fromEntries(
        subtitles
          .filter((v) => v.id !== HASH_CACHEAT_KEY)
          .map((s): [string, YtdlpVideoSubtitleListCacheItem] => [
            s.id,
            pick(s, ["ast", "name"]),
          ]),
      ),
      [HASH_CACHEAT_KEY]: Math.floor(Date.now() / 1000),
    };

    redis.hset<number | string>(hashKey, records);
    redis.expire(hashKey, CACHE_EXPIRATION.subtitleList);
  }

  /**
   * Check if a specific subtitle exists in the cached subtitle list
   */
  async hasSubtitle(
    videoId: string,
    subtitleId: string,
  ): Promise<boolean | null> {
    if (subtitleId === HASH_CACHEAT_KEY) return false;

    const hashKey = CACHE_KEYS.subtitleListHash(videoId);
    const [cacheExists, hashExists] = await this.#redis
      .pipeline()
      .exists(hashKey)
      .hexists(hashKey, subtitleId)
      .exec();

    if (cacheExists === 0) return null;
    return cacheExists === 1 && hashExists === 1;
  }

  /**
   * Get cached subtitle URL (checks hash-based storage first, then individual cache)
   */
  async getSubtitleUrl(
    videoId: string,
    subtitleId: string,
  ): Promise<{ url: string; isStale: boolean } | null> {
    if (subtitleId === HASH_CACHEAT_KEY) return null;

    logger.debug("Fetching cached subtitle URL", { videoId, subtitleId });

    // First check hash-based storage
    const hashKey = CACHE_KEYS.subtitleUrlHash(videoId);
    const [url, cachedAt, hashTtl, [keyTtl]] = await this.#redis
      .pipeline()
      .hget<string>(hashKey, subtitleId)
      .hget<number>(hashKey, HASH_CACHEAT_KEY)
      .ttl(hashKey)
      .httl(hashKey, subtitleId)
      .exec();

    if (!url) {
      logger.debug("Subtitle URL cache miss", {
        videoId,
        subtitleId,
      });
      return null;
    }

    const expiry = parseExpirationFromSubtitleUrl(url);

    if (expiry.exat === null) {
      const isStale = this.#isStale(keyTtl ?? hashTtl ?? 0, {
        ex: CACHE_EXPIRATION.subtitle,
      });

      logger.info("Subtitle URL cache hit (no URL expiration)", {
        videoId,
        subtitleId,
        isStale,
        ttl: keyTtl ?? hashTtl,
      });

      return { url, isStale };
    }

    if (cachedAt === null) {
      logger.warn("No cached_at found for subtitle hash", {
        videoId,
        subtitleId,
        hashKey,
      });
      return { url, isStale: false };
    }

    const isStale = this.#isStale(keyTtl ?? hashTtl ?? 0, {
      exat: expiry.exat,
      cached_at: cachedAt,
    });

    logger.info("Subtitle URL cache hit (with URL expiration)", {
      videoId,
      subtitleId,
      isStale,
      urlExpiration: expiry.exat,
      cachedAt,
      ttl: keyTtl ?? hashTtl,
    });

    return { url, isStale };
  }

  /**
   * Cache multiple subtitle URLs in a hash with shared minimum expiration
   * Logs min/max/avg expiration statistics
   */
  async setSubtitleUrlsHash(
    videoId: string,
    subtitles: { id: string; url: string }[],
    { redis = this.#redis }: { redis?: Redis | Pipeline } = {},
  ): Promise<void> {
    if (subtitles.length === 0) return;
    const hashKey = CACHE_KEYS.subtitleUrlHash(videoId);
    const records = {
      ...Object.fromEntries(
        subtitles
          .filter((v) => v.id !== HASH_CACHEAT_KEY)
          .map((s) => [s.id, s.url]),
      ),
      // use unix timestamp in seconds
      [HASH_CACHEAT_KEY]: Math.floor(Date.now() / 1000),
    };
    redis.hset<number | string>(hashKey, records);
    const expirations = Map.groupBy(
      subtitles.map((s) => ({
        ...parseExpirationFromSubtitleUrl(s.url),
        ...s,
      })),
      (e) => e.exat,
    );
    // if the whole set of url shared the same expiration, set the expiration at hash key
    if (expirations.size === 1) {
      if (expirations.get(null)) {
        redis.expire(hashKey, CACHE_EXPIRATION.subtitle);
      } else {
        const [grouped] = expirations.values();
        const exat = grouped![0]!.exat!;
        redis.expireat(hashKey, exat);
      }
    } else {
      for (const [exat, grouped] of expirations) {
        if (exat === null) {
          redis.hexpire(
            hashKey,
            grouped.map((e) => e.id),
            CACHE_EXPIRATION.subtitle,
          );
        } else {
          redis.hexpireat(
            hashKey,
            grouped.map((e) => e.id),
            exat,
          );
        }
      }
    }
  }

  /**
   * Get cached YouTube API metadata with stale check
   */
  async getYouTubeApiMetadata(
    videoId: string,
  ): Promise<CacheResult<YouTubeApiMetadata>> {
    logger.debug("Fetching cached YouTube API metadata", { videoId });

    const cacheKey = CACHE_KEYS.youtubeApiMetadata(videoId);

    const [data, ttl] = await this.#redis
      .pipeline()
      .get<YouTubeApiMetadata>(cacheKey)
      .ttl(cacheKey)
      .exec();

    if (!data) {
      logger.debug("YouTube API metadata cache miss", {
        videoId,
      });
      return null;
    }

    const isStale = this.#isStale(ttl, { ex: CACHE_EXPIRATION.youtubeApi });

    logger.info("YouTube API metadata cache hit", {
      videoId,
      title: data.title,
      isStale,
      ttl,
    });

    return {
      data,
      isStale,
    };
  }

  /**
   * Cache YouTube API metadata with Redis TTL
   */
  async setYouTubeApiMetadata(
    videoId: string,
    metadata: YouTubeApiMetadata,
    { redis = this.#redis }: { redis?: Redis | Pipeline } = {},
  ): Promise<void> {
    const cacheKey = CACHE_KEYS.youtubeApiMetadata(videoId);
    await redis.set(cacheKey, metadata, {
      ex: CACHE_EXPIRATION.youtubeApi,
    });
  }

  /**
   * Get cached YouTube API subtitle list with stale check
   */
  async getYouTubeApiSubtitleList(
    videoId: string,
  ): Promise<CacheResult<YouTubeApiSubtitle[]>> {
    const cacheKey = CACHE_KEYS.youtubeApiSubtitles(videoId);

    // Use Redis pipeline to get both data and TTL in single call
    const [data, ttl] = await this.#redis
      .pipeline()
      .get<YouTubeApiSubtitle[]>(cacheKey)
      .ttl(cacheKey)
      .exec();

    if (!data) return null;

    const isStale = this.#isStale(ttl, { ex: CACHE_EXPIRATION.youtubeApi });

    return {
      data,
      isStale,
    };
  }

  /**
   * Cache YouTube API subtitle list with Redis TTL
   */
  async setYouTubeApiSubtitleList(
    videoId: string,
    subtitles: YouTubeApiSubtitle[],
    { redis = this.#redis }: { redis?: Redis | Pipeline } = {},
  ): Promise<void> {
    const cacheKey = CACHE_KEYS.youtubeApiSubtitles(videoId);
    await redis.set(cacheKey, subtitles, {
      ex: CACHE_EXPIRATION.youtubeApiSubtitles,
    });
  }

  /**
   * Check if a video is cached as not found (404)
   */
  async isNotFound(videoId: string): Promise<boolean> {
    logger.debug("Checking 404 tombstone cache", { videoId });

    const cacheKey = CACHE_KEYS.notFound(videoId);
    const exists = await this.#redis.exists(cacheKey);

    const isNotFound = exists === 1;

    if (isNotFound) {
      logger.info("Video found in 404 tombstone cache", { videoId });
    } else {
      logger.debug("Video not in 404 tombstone cache", { videoId });
    }

    return isNotFound;
  }

  /**
   * Cache a video as not found (404) with tombstone entry
   */
  async setNotFound(
    videoId: string,
    { redis = this.#redis }: { redis?: Redis | Pipeline } = {},
  ): Promise<void> {
    logger.info("Caching video as not found (404)", {
      videoId,
      expiration: CACHE_EXPIRATION.notFound,
    });

    const cacheKey = CACHE_KEYS.notFound(videoId);
    await redis.set(cacheKey, 1, {
      ex: CACHE_EXPIRATION.notFound,
    });

    logger.debug("Video cached as 404 tombstone", { videoId });
  }

  /**
   * Get cached cleaned description by content hash
   */
  async getCleanedDescription(contentHash: string): Promise<string | null> {
    logger.debug("Fetching cached cleaned description", { contentHash });

    const cacheKey = CACHE_KEYS.cleanedDescription(contentHash);
    const result = await this.#redis.getex<string>(cacheKey, {
      ex: CACHE_EXPIRATION.cleanedDescription,
    });

    if (result) {
      logger.info("Cleaned description cache hit", {
        contentHash,
        resultLength: JSON.stringify(result).length,
      });
    } else {
      logger.debug("Cleaned description cache miss", { contentHash });
    }

    return result;
  }

  /**
   * Cache cleaned description by content hash
   */
  async setCleanedDescription(
    contentHash: string,
    cleanedDescription: string,
    { redis = this.#redis }: { redis?: Redis | Pipeline } = {},
  ): Promise<void> {
    logger.info("Caching cleaned description", {
      contentHash,
      dataLength: cleanedDescription.length,
      expiration: CACHE_EXPIRATION.cleanedDescription,
    });

    const cacheKey = CACHE_KEYS.cleanedDescription(contentHash);
    await redis.set(cacheKey, cleanedDescription, {
      ex: CACHE_EXPIRATION.cleanedDescription,
    });

    logger.debug("Cleaned description cached successfully", { contentHash });
  }
}

/**
 * Parse expiration seconds from YouTube URL or use fallback
 * @returns expiration unix timestamp in seconds
 */
function parseExpirationFromSubtitleUrl(url: string): { exat: number | null } {
  try {
    const urlObj = new URL(url);
    const expires = urlObj.searchParams.get("expire");
    if (!expires) throw new Error("No expiration found");

    const expirationTimestamp = Number.parseInt(expires, 10);
    if (Number.isNaN(expirationTimestamp))
      throw new Error("Invalid expiration timestamp");

    return { exat: expirationTimestamp };
  } catch (error) {
    logger.warn("Failed to parse YouTube URL expiration", {
      url: redactSensitiveData({ url }).url,
      error: errorToJSON(error),
    });
    return { exat: null };
  }
}
