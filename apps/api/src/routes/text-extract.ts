import { Hono } from "hono";
import type { HonoEnv } from "../types/env";
import { GeminiService } from "../services/gemini/service";
import { createMiddleware } from "hono/factory";
import { getLogger } from "@logtape/logtape";

import { errorToJSON } from "../lib/logging";
import { vValidator } from "@hono/valibot-validator";
import { TextExtractFormDataSchema } from "../services/gemini/validation";

const logger = getLogger(["mx-api", "text-extract"]);

// Extend the environment type to include our services
type ServiceVariables = {
  services: {
    gemini: GeminiService;
  };
};

type ExtendedHonoEnv = HonoEnv & {
  Variables: ServiceVariables;
};

// Create a middleware to initialize services
const servicesMiddleware = createMiddleware<ExtendedHonoEnv>(
  async (c, next) => {
    const geminiService = new GeminiService(c.env.GOOGLE_API_KEY);

    c.set("services", {
      gemini: geminiService,
    });

    await next();
  },
);

const app = new Hono<ExtendedHonoEnv>()
  .use(servicesMiddleware)
  .post("/", vValidator("form", TextExtractFormDataSchema), async (c) => {
    logger.info("Processing text extraction request");

    const { services } = c.var;
    const { image } = c.req.valid("form");

    try {
      logger.debug("Processing validated image file", {
        size: image.size,
        type: image.type,
      });

      // Call Gemini service to extract text
      const startTime = Date.now();
      const result = await services.gemini.extractText(image);
      const duration = Date.now() - startTime;

      logger.info("Text extraction completed successfully", {
        duration,
        status: result.status,
        contentLength: result.content.length,
        metadata: result.metadata,
      });

      // Return the result in the expected format
      return c.json(result);
    } catch (error) {
      // Handle other errors
      logger.error("Text extraction failed", {
        error: errorToJSON(error),
      });

      return c.json(
        {
          status: "error",
          content: "Text extraction failed",
          metadata: {
            has_tables: false,
            has_lists: false,
            text_regions_count: 0,
          },
        },
        500,
      );
    }
  });

export default app;
