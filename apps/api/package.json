{"name": "@mx/api", "type": "module", "scripts": {"dev": "wrangler dev -e development", "deploy:dev": "wrangler deploy --minify -e development", "deploy:prod": "wrangler deploy --minify -e production", "gen:types": "wrangler types", "gen:yt-dlp:client": "openapi-ts -i ./spec/yt-dlp.spec.json -o ./src/client/yt-dlp", "gen:apify:client": "openapi-ts -i http://docs.apify.com/api/openapi.json -o ./src/client/apify", "gen:yt-dlp:info": "json2ts -i ./spec/yt-dlp.info.json -o ./src/client/yt-dlp/info.ts"}, "dependencies": {"@google/genai": "^1.7.0", "@hono-rate-limiter/redis": "^0.1.4", "@hono/valibot-validator": "^0.5.2", "@logtape/logtape": "^1.0.0", "@mx/db": "workspace:*", "@mx/shared": "workspace:*", "@std/assert": "npm:@jsr/std__assert@^1.0.13", "@std/async": "npm:@jsr/std__async@^1.0.13", "@std/collections": "npm:@jsr/std__collections@^1.1.1", "@std/encoding": "npm:@jsr/std__encoding@^1.0.10", "@supabase/ssr": "^0.5.2", "@supabase/supabase-js": "^2.47.12", "@upstash/redis": "^1.35.0", "@valibot/to-json-schema": "^1.3.0", "dayjs": "^1.11.13", "drizzle-orm": "^0.38.4", "hono": "^4.8.2", "hono-rate-limiter": "^0.4.2", "jose": "^5.9.6", "nanoid": "^5.0.9", "postgres": "^3.4.5", "valibot": "^1.1.0"}, "devDependencies": {"@cloudflare/vitest-pool-workers": "^0.8.43", "@hey-api/openapi-ts": "^0.73.0", "googleapis": "^146.0.0", "json-schema-to-typescript": "^15.0.4", "typescript": "~5.8.2", "vitest": "~3.2.0", "wrangler": "4.22.0"}}